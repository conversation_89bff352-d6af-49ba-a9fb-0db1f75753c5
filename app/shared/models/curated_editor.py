"""
Models for curated content editor operations.

This module contains Pydantic models used for curated content editing,
including request/response models for CRUD operations on curated sets and tasks.
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime


class CuratedSetResponse(BaseModel):
    """Response model for curated set data."""
    id: str = Field(..., description="Curated set ID")
    title: str = Field(..., description="Set title")
    input_type: str = Field(..., description="Input type (text, audio, etc.)")
    theme_id: Optional[str] = Field(None, description="Associated theme ID")
    tasks: List[str] = Field(..., description="List of task IDs")
    stories: List[str] = Field(..., description="List of story IDs")
    total_tasks: int = Field(..., description="Total number of tasks")
    total_stories: int = Field(..., description="Total number of stories")
    text_tasks_ready: int = Field(..., description="Number of text tasks ready")
    media_tasks_pending: int = Field(..., description="Number of media tasks pending")
    status: str = Field(..., description="Set status")
    gentype: str = Field(..., description="Generation type")
    has_follow_up: bool = Field(..., description="Whether set has follow-up content")
    difficulty_level: Optional[int] = Field(None, description="Difficulty level (1-3)")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    class Config:
        from_attributes = True


class CuratedSetUpdate(BaseModel):
    """Model for updating curated set properties."""
    title: Optional[str] = Field(None, description="Set title")
    status: Optional[str] = Field(None, description="Set status")
    difficulty_level: Optional[int] = Field(None, ge=1, le=3, description="Difficulty level (1-3)")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class TaskItemBasic(BaseModel):
    """Basic task item information for listing."""
    id: str = Field(..., description="Task item ID")
    type: str = Field(..., description="Task type")
    title: str = Field(..., description="Task title")
    status: str = Field(..., description="Task status")
    difficulty_level: int = Field(..., description="Difficulty level")
    total_score: int = Field(..., description="Total score for task")
    
    class Config:
        from_attributes = True


class QuestionData(BaseModel):
    """Question data structure."""
    text: str = Field(..., description="Question text in Nepali")
    translated_text: Optional[str] = Field(None, description="Question text in English")
    options:Dict[str, str] = Field(..., description="Answer options in Nepali")
    options_en: Optional[Dict[str, str]] = Field(None, description="Answer options in English")
    correct_answer_index: int = Field(..., description="Index of correct answer")
    audio_metadata: Optional[Dict[str, Any]] = Field(None, description="Audio metadata")
    image_metadata: Optional[Dict[str, Any]] = Field(None, description="Image metadata")
    options_metadata: Optional[Dict[str, Any]] = Field(None, description="Options metadata")


class CorrectAnswerData(BaseModel):
    """Correct answer data structure."""
    text: str = Field(..., description="Correct answer text")
    index: int = Field(..., description="Correct answer index")
    explanation: Optional[str] = Field(None, description="Answer explanation")


class TaskItemDetailed(BaseModel):
    """Detailed task item with all properties."""
    id: str = Field(..., description="Task item ID")
    type: str = Field(..., description="Task type")
    title: str = Field(..., description="Task title")
    question: QuestionData = Field(..., description="Question data")
    correct_answer: CorrectAnswerData = Field(..., description="Correct answer data")
    status: str = Field(..., description="Task status")
    difficulty_level: int = Field(..., description="Difficulty level")
    total_score: int = Field(..., description="Total score for task")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    
    class Config:
        from_attributes = True


class TaskItemUpdate(BaseModel):
    """Model for updating task item properties."""
    title: Optional[str] = Field(None, description="Task title")
    question: Optional[QuestionData] = Field(None, description="Question data")
    correct_answer: Optional[CorrectAnswerData] = Field(None, description="Correct answer data")
    difficulty_level: Optional[int] = Field(None, ge=1, le=3, description="Difficulty level (1-3)")
    total_score: Optional[int] = Field(None, ge=1, description="Total score for task")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class CuratedSetListResponse(BaseModel):
    """Response model for curated sets list with pagination."""
    sets: List[CuratedSetResponse] = Field(..., description="List of curated sets")
    pagination: Dict[str, Any] = Field(..., description="Pagination metadata")
    filters: Dict[str, Any] = Field(..., description="Applied filters")


class TaskItemListResponse(BaseModel):
    """Response model for task items list."""
    tasks: List[TaskItemBasic] = Field(..., description="List of task items")
    set_info: Dict[str, Any] = Field(..., description="Curated set information")
    total_count: int = Field(..., description="Total number of tasks in set")
