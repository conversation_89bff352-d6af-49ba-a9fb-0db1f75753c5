"""
Routes for theme management - create and update theme operations.
"""

from fastapi import APIRouter, Depends, HTTPException
from typing import Dict, Any, Optional
from bson import ObjectId
from pymongo.errors import PyMongoError
from datetime import datetime, timezone
from app.shared.security import get_tenant_info, require_roles
from app.shared.models.user import UserTenantDB
from app.shared.models.theme import ThemeCreate, ThemeUpdate, ThemeColorUpdate, ThemeQuestionCreate
from app.shared.utils.logger import setup_new_logging
from app.shared.api_response import APIResponse, ResponseMetadata
from app.shared.utils.mongodb import convert_object_ids

# Configure logging
loggers = setup_new_logging(__name__)

router = APIRouter()





@router.post("/themes", response_model=APIResponse[Dict[str, Any]])
async def create_theme(
    theme_data: ThemeCreate,
    user_tenant: UserTenantDB = Depends(get_tenant_info)
) -> APIResponse[Dict[str, Any]]:
    """
    Create a new theme with background and font colors.
    
    This endpoint creates a new theme that can be used for curated content generation.
    Each theme includes visual styling with background and font colors.
    """
    try:
        # Check if theme with same name already exists
        existing_theme = await user_tenant.async_db.themes.find_one({
            "$or": [
                {"name": theme_data.name},
                {"name_en": theme_data.name_en}
            ]
        })
        
        if existing_theme:
            raise HTTPException(
                status_code=400, 
                detail="Theme with this name already exists"
            )

        # Prepare theme document
        theme_doc = {
            "name": theme_data.name,
            "name_en": theme_data.name_en,
            "description": theme_data.description,
            "description_en": theme_data.description_en,
            "category": theme_data.category,
            "icon": theme_data.icon,
            "background_color": theme_data.background_color,
            "font_color": theme_data.font_color,
            "color": theme_data.background_color,  # Keep backward compatibility
            "is_active": theme_data.is_active,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            "created_by": user_tenant.user.id
        }

        # Insert theme into database
        result = await user_tenant.async_db.themes.insert_one(theme_doc)
        
        # Get the created theme
        created_theme = await user_tenant.async_db.themes.find_one(
            {"_id": result.inserted_id}
        )
        
        # Convert ObjectIds to strings
        created_theme = convert_object_ids(created_theme)

        return APIResponse[Dict[str, Any]](
            success=True,
            data=created_theme,
            message="Theme created successfully",
            metadata=ResponseMetadata(
                timestamp=None,
                request_id=None
            )
        )

    except HTTPException:
        raise
    except PyMongoError as e:
        loggers.error(f"Database error in create_theme: {e}")
        raise HTTPException(status_code=500, detail="Database error occurred")
    except Exception as e:
        loggers.error(f"Unexpected error in create_theme: {e}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


@router.put("/themes/{theme_id}", response_model=APIResponse[Dict[str, Any]])
async def update_theme(
    theme_id: str,
    theme_data: ThemeUpdate,
    user_tenant: UserTenantDB = Depends(get_tenant_info)
) -> APIResponse[Dict[str, Any]]:
    """
    Update an existing theme's background color, font color, or other properties.
    
    This endpoint allows updating theme properties including visual styling.
    Only provided fields will be updated, others remain unchanged.
    
    Path Parameters:
    - theme_id: The ID of the theme to update
    """
    try:
        # Validate theme_id
        if not ObjectId.is_valid(theme_id):
            raise HTTPException(status_code=400, detail="Invalid theme ID format")

        # Check if theme exists
        existing_theme = await user_tenant.async_db.themes.find_one(
            {"_id": ObjectId(theme_id)}
        )
        
        if not existing_theme:
            raise HTTPException(status_code=404, detail="Theme not found")

        # Build update document with only provided fields
        update_doc = {"updated_at": datetime.now(timezone.utc)}
        
        # Update fields if provided
        if theme_data.name is not None:
            update_doc["name"] = theme_data.name
        if theme_data.name_en is not None:
            update_doc["name_en"] = theme_data.name_en
        if theme_data.description is not None:
            update_doc["description"] = theme_data.description
        if theme_data.description_en is not None:
            update_doc["description_en"] = theme_data.description_en
        if theme_data.category is not None:
            update_doc["category"] = theme_data.category
        if theme_data.icon is not None:
            update_doc["icon"] = theme_data.icon
        if theme_data.background_color is not None:
            update_doc["background_color"] = theme_data.background_color
            update_doc["color"] = theme_data.background_color  # Keep backward compatibility
        if theme_data.font_color is not None:
            update_doc["font_color"] = theme_data.font_color
        if theme_data.is_active is not None:
            update_doc["is_active"] = theme_data.is_active

        # Check for duplicate names if name is being updated
        if theme_data.name or theme_data.name_en:
            name_query = {"_id": {"$ne": ObjectId(theme_id)}}
            if theme_data.name:
                name_query["name"] = theme_data.name
            elif theme_data.name_en:
                name_query["name_en"] = theme_data.name_en
                
            duplicate_theme = await user_tenant.async_db.themes.find_one(name_query)
            if duplicate_theme:
                raise HTTPException(
                    status_code=400,
                    detail="Another theme with this name already exists"
                )

        # Update the theme
        await user_tenant.async_db.themes.update_one(
            {"_id": ObjectId(theme_id)},
            {"$set": update_doc}
        )

        # Get the updated theme
        updated_theme = await user_tenant.async_db.themes.find_one(
            {"_id": ObjectId(theme_id)}
        )
        
        # Convert ObjectIds to strings
        updated_theme = convert_object_ids(updated_theme)

        return APIResponse[Dict[str, Any]](
            success=True,
            data=updated_theme,
            message="Theme updated successfully",
            metadata=ResponseMetadata(
                timestamp=None,
                request_id=None
            )
        )

    except HTTPException:
        raise
    except PyMongoError as e:
        loggers.error(f"Database error in update_theme: {e}")
        raise HTTPException(status_code=500, detail="Database error occurred")
    except Exception as e:
        loggers.error(f"Unexpected error in update_theme: {e}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


@router.patch("/themes/{theme_id}/colors", response_model=APIResponse[Dict[str, Any]])
async def update_theme_colors(
    theme_id: str,
    background_color: Optional[str] = None,
    font_color: Optional[str] = None,
    user_tenant: UserTenantDB = Depends(get_tenant_info)
) -> APIResponse[Dict[str, Any]]:
    """
    Update only the colors of an existing theme.
    
    This is a convenience endpoint for updating just the background and/or font colors
    of a theme without affecting other properties.
    
    Path Parameters:
    - theme_id: The ID of the theme to update
    
    Query Parameters:
    - background_color: New background color (hex format, optional)
    - font_color: New font color (hex format, optional)
    """
    try:
        # Validate theme_id
        if not ObjectId.is_valid(theme_id):
            raise HTTPException(status_code=400, detail="Invalid theme ID format")

        # Check if at least one color is provided
        if not background_color and not font_color:
            raise HTTPException(
                status_code=400, 
                detail="At least one color (background_color or font_color) must be provided"
            )

        # Check if theme exists
        existing_theme = await user_tenant.async_db.themes.find_one(
            {"_id": ObjectId(theme_id)}
        )
        
        if not existing_theme:
            raise HTTPException(status_code=404, detail="Theme not found")

        # Build update document
        update_doc = {"updated_at": datetime.now(timezone.utc)}
        
        if background_color:
            update_doc["background_color"] = background_color
            update_doc["color"] = background_color  # Keep backward compatibility
            
        if font_color:
            update_doc["font_color"] = font_color

        # Update the theme
        await user_tenant.async_db.themes.update_one(
            {"_id": ObjectId(theme_id)},
            {"$set": update_doc}
        )

        # Get the updated theme
        updated_theme = await user_tenant.async_db.themes.find_one(
            {"_id": ObjectId(theme_id)}
        )
        
        # Convert ObjectIds to strings
        updated_theme = convert_object_ids(updated_theme)

        return APIResponse[Dict[str, Any]](
            success=True,
            data=updated_theme,
            message="Theme colors updated successfully",
            metadata=ResponseMetadata(
                timestamp=None,
                request_id=None
            )
        )

    except HTTPException:
        raise
    except PyMongoError as e:
        loggers.error(f"Database error in update_theme_colors: {e}")
        raise HTTPException(status_code=500, detail="Database error occurred")
    except Exception as e:
        loggers.error(f"Unexpected error in update_theme_colors: {e}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")
    
@router.delete("/themes/{theme_id}", response_model=APIResponse[Dict[str, Any]])
async def delete_theme(
    theme_id: str,
    user_tenant: UserTenantDB = Depends(get_tenant_info)
) -> APIResponse[Dict[str, Any]]:
    """
    Delete an existing theme.
    
    This endpoint allows deleting a theme by its ID.
    
    Path Parameters:
    - theme_id: The ID of the theme to delete
    """
    try:
        # Validate theme_id
        if not ObjectId.is_valid(theme_id):
            raise HTTPException(status_code=400, detail="Invalid theme ID format")

        # Check if theme exists
        existing_theme = await user_tenant.async_db.themes.find_one(
            {"_id": ObjectId(theme_id)}
        )
        
        if not existing_theme:
            raise HTTPException(status_code=404, detail="Theme not found")

        # Delete the theme
        await user_tenant.async_db.themes.delete_one({"_id": ObjectId(theme_id)})

        return APIResponse[Dict[str, Any]](
            success=True,
            data={},
            message="Theme deleted successfully",
            metadata=ResponseMetadata(
                timestamp=None,
                request_id=None
            )
        )

    except HTTPException:
        raise
    except PyMongoError as e:
        loggers.error(f"Database error in delete_theme: {e}")
        raise HTTPException(status_code=500, detail="Database error occurred")
    except Exception as e:
        loggers.error(f"Unexpected error in delete_theme: {e}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


@router.post("/themes/questions", response_model=APIResponse[Dict[str, Any]])
async def create_theme_question(
    question_data: ThemeQuestionCreate,
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> APIResponse[Dict[str, Any]]:
    """
    Create a new question for a theme.

    This endpoint creates a new question that can be used for theme engagement.
    Each question includes text in Nepali and optionally in English.

    Request Body:
    - theme_id: The ID of the theme this question belongs to
    - text: Question text in Nepali (required)
    - text_en: Question text in English (optional)
    """
    try:
        # Validate theme_id
        if not ObjectId.is_valid(question_data.theme_id):
            raise HTTPException(status_code=400, detail="Invalid theme ID format")

        # Check if theme exists
        existing_theme = await user_tenant.async_db.themes.find_one({
            "_id": ObjectId(question_data.theme_id)
        })

        if not existing_theme:
            raise HTTPException(status_code=404, detail="Theme not found")

        # Prepare question document
        question_doc = {
            "theme_id": ObjectId(question_data.theme_id),
            "text": question_data.text,
            "text_en": question_data.text_en,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        }

        # Insert question into database
        result = await user_tenant.async_db.themes_questions.insert_one(question_doc)

        # Get the created question
        created_question = await user_tenant.async_db.themes_questions.find_one(
            {"_id": result.inserted_id}
        )

        # Convert ObjectIds to strings
        created_question = convert_object_ids(created_question)

        return APIResponse[Dict[str, Any]](
            success=True,
            data=created_question,
            message="Theme question created successfully",
            metadata=ResponseMetadata(
                timestamp=None,
                request_id=None
            )
        )

    except HTTPException:
        raise
    except PyMongoError as e:
        loggers.error(f"Database error in create_theme_question: {e}")
        raise HTTPException(status_code=500, detail="Database error occurred")
    except Exception as e:
        loggers.error(f"Unexpected error in create_theme_question: {e}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")